package com.unipus.digitalbook.service;

import com.unipus.digitalbook.conf.feishu.FeishuBotProperties;
import com.unipus.digitalbook.service.impl.FeishuBotServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 飞书机器人服务测试类
 */
@ExtendWith(MockitoExtension.class)
class FeishuBotServiceTest {

    @Mock
    private FeishuBotProperties feishuBotProperties;

    @InjectMocks
    private FeishuBotServiceImpl feishuBotService;

    @BeforeEach
    void setUp() {
        // 设置测试环境的配置
        when(feishuBotProperties.getGrafanaBaseUrl()).thenReturn("http://************:31588");
        when(feishuBotProperties.getGrafanaDatasourceUid()).thenReturn("ab10533a-937d-40e0-bb29-cb9ff814fe1a");
        when(feishuBotProperties.getGrafanaAppName()).thenReturn("digitalbook");
        when(feishuBotProperties.getGrafanaTimeRange()).thenReturn("now-2h");
        when(feishuBotProperties.getGrafanaTimeRangeTo()).thenReturn("now");
    }

    @Test
    void testBuildGrafanaUrl_LocalEnvironment() {
        // 测试本地环境的URL生成
        String requestId = "test-request-123";
        String expectedUrl = "http://************:31588/explore?orgId=1&left={\"datasource\":\"ab10533a-937d-40e0-bb29-cb9ff814fe1a\",\"queries\":[{\"refId\":\"A\",\"expr\":\"{app=\\\"digitalbook\\\"}|=`test-request-123`\",\"queryType\":\"range\",\"datasource\":{\"type\":\"loki\",\"uid\":\"ab10533a-937d-40e0-bb29-cb9ff814fe1a\"},\"editorMode\":\"builder\"}],\"range\":{\"from\":\"now-2h\",\"to\":\"now\"}}";
        
        String actualUrl = ReflectionTestUtils.invokeMethod(feishuBotService, "buildGrafanaUrl", requestId);
        
        assertEquals(expectedUrl, actualUrl);
    }

    @Test
    void testBuildGrafanaUrl_ProductionEnvironment() {
        // 测试正式环境的URL生成
        when(feishuBotProperties.getGrafanaBaseUrl()).thenReturn("http://************:31298");
        when(feishuBotProperties.getGrafanaDatasourceUid()).thenReturn("fXtMzrNSz");
        when(feishuBotProperties.getGrafanaAppName()).thenReturn("ipublish-api");
        when(feishuBotProperties.getGrafanaTimeRange()).thenReturn("now-5m");
        
        String requestId = "prod-request-456";
        String expectedUrl = "http://************:31298/explore?orgId=1&left={\"datasource\":\"fXtMzrNSz\",\"queries\":[{\"refId\":\"A\",\"expr\":\"{app=\\\"ipublish-api\\\"}|=`prod-request-456`\",\"queryType\":\"range\",\"datasource\":{\"type\":\"loki\",\"uid\":\"fXtMzrNSz\"},\"editorMode\":\"builder\"}],\"range\":{\"from\":\"now-5m\",\"to\":\"now\"}}";
        
        String actualUrl = ReflectionTestUtils.invokeMethod(feishuBotService, "buildGrafanaUrl", requestId);
        
        assertEquals(expectedUrl, actualUrl);
    }

    @Test
    void testBuildGrafanaUrl_WithSpecialCharacters() {
        // 测试包含特殊字符的requestId
        String requestId = "req-123_456@test";
        String encodedRequestId = "req-123_456%40test"; // @ 被编码为 %40
        
        String actualUrl = ReflectionTestUtils.invokeMethod(feishuBotService, "buildGrafanaUrl", requestId);
        
        assertTrue(actualUrl.contains(encodedRequestId));
        assertFalse(actualUrl.contains(requestId)); // 原始requestId不应该出现在URL中
    }

    @Test
    void testBuildGrafanaUrl_EmptyRequestId() {
        // 测试空requestId的情况
        String requestId = "";
        
        String actualUrl = ReflectionTestUtils.invokeMethod(feishuBotService, "buildGrafanaUrl", requestId);
        
        assertNotNull(actualUrl);
        assertTrue(actualUrl.contains("|=``")); // 空requestId应该生成空的查询条件
    }

    @Test
    void testBuildGrafanaUrl_NullRequestId() {
        // 测试null requestId的情况
        String actualUrl = ReflectionTestUtils.invokeMethod(feishuBotService, "buildGrafanaUrl", (String) null);
        
        assertNotNull(actualUrl);
        assertTrue(actualUrl.contains("|=`null`")); // null requestId应该生成"null"字符串
    }
} 