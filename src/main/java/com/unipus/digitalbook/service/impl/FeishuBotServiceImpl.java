package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.conf.feishu.FeishuBotProperties;
import com.unipus.digitalbook.model.dto.feishu.FeishuCardMessage;
import com.unipus.digitalbook.model.dto.feishu.FeishuMessage;
import com.unipus.digitalbook.model.dto.feishu.FeishuPostMessage;
import com.unipus.digitalbook.model.dto.feishu.FeishuTextMessage;
import com.unipus.digitalbook.service.FeishuBotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 飞书机器人服务实现类
 */
@Slf4j
@Service
public class FeishuBotServiceImpl implements FeishuBotService {
    
    @Autowired
    private FeishuBotProperties feishuBotProperties;
    
    @Autowired
    private RestTemplate restTemplate;

    @Override
    public boolean sendTextMessage(String text) {
        FeishuTextMessage textMessage = new FeishuTextMessage();
        textMessage.setText(text);
        return sendTextMessage(textMessage);
    }
    
    @Override
    public boolean sendTextMessage(FeishuTextMessage textMessage) {
        try {
            FeishuMessage message = new FeishuMessage();
            message.setMsgType("text");
            message.setContent(textMessage);
            
            return sendMessage(message);
        } catch (Exception e) {
            log.error("发送文本消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendPostMessage(FeishuPostMessage postMessage) {
        try {
            // 使用正确的post消息格式
            FeishuMessage message = new FeishuMessage();
            message.setMsgType("post");
            // 创建正确的content结构：{ "post": { ... } }
            Map<String, Object> content = new HashMap<>();
            content.put("post", postMessage.getPost());
            message.setContent(content);
            
            return sendMessage(message);
        } catch (Exception e) {
            log.error("发送富文本消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendCardMessage(FeishuCardMessage cardMessage) {
        try {
            FeishuMessage message = new FeishuMessage();
            message.setMsgType("interactive");
            // 卡片消息直接使用整个cardMessage对象
            message.setCard(cardMessage);
            
            return sendMessage(message);
        } catch (Exception e) {
            log.error("发送卡片消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendCustomMessage(String messageType, Object content) {
        try {
            FeishuMessage message = new FeishuMessage();
            message.setMsgType(messageType);
            message.setContent(content);
            
            return sendMessage(message);
        } catch (Exception e) {
            log.error("发送自定义消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendNotification(String title, String content) {
        try {
            // 创建卡片消息
            FeishuCardMessage cardMessage = new FeishuCardMessage();
            
            // 设置卡片头部
            FeishuCardMessage.CardHeader header = new FeishuCardMessage.CardHeader();
            FeishuCardMessage.CardTitle cardTitle = new FeishuCardMessage.CardTitle();
            cardTitle.setContent(title);
            cardTitle.setTag("plain_text");
            header.setTitle(cardTitle);
            cardMessage.setHeader(header);
            
            // 设置卡片主体
            FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
            List<FeishuCardMessage.CardElement> elements = new ArrayList<>();
            
            // 添加内容元素
            FeishuCardMessage.CardElement contentElement = new FeishuCardMessage.CardElement();
            contentElement.setTag("markdown");
            contentElement.setContent(content);
            contentElement.setTextAlign("left");
            contentElement.setTextSize("normal_v2");
            contentElement.setMargin("0px 0px 0px 0px");
            elements.add(contentElement);
            
            // 添加时间元素
            FeishuCardMessage.CardElement timeElement = new FeishuCardMessage.CardElement();
            timeElement.setTag("hr");
            timeElement.setMargin("4px 0px 0px 0px");
            elements.add(timeElement);
            
            FeishuCardMessage.CardElement timeTextElement = new FeishuCardMessage.CardElement();
            timeTextElement.setTag("markdown");
            timeTextElement.setContent("发送时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            timeTextElement.setTextAlign("left");
            timeTextElement.setMargin("4px 0px 0px 0px");
            elements.add(timeTextElement);
            
            body.setElements(elements);
            cardMessage.setBody(body);
            
            return sendCardMessage(cardMessage);
        } catch (Exception e) {
            log.error("发送通知消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendErrorNotification(String error, String context) {
        try {
            // 创建错误通知卡片
            FeishuCardMessage cardMessage = new FeishuCardMessage();
            
            // 设置卡片头部
            FeishuCardMessage.CardHeader header = new FeishuCardMessage.CardHeader();
            FeishuCardMessage.CardTitle cardTitle = new FeishuCardMessage.CardTitle();
            cardTitle.setContent("❌ 系统错误通知");
            cardTitle.setTag("plain_text");
            header.setTitle(cardTitle);
            cardMessage.setHeader(header);
            
            // 设置卡片主体
            FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
            List<FeishuCardMessage.CardElement> elements = new ArrayList<>();
            
            // 添加错误信息元素
            FeishuCardMessage.CardElement errorElement = new FeishuCardMessage.CardElement();
            errorElement.setTag("markdown");
            errorElement.setContent("**错误信息：**\n" + error);
            errorElement.setTextAlign("left");
            errorElement.setMargin("0px 0px 0px 0px");
            elements.add(errorElement);
            
            // 添加上下文信息元素
            if (context != null && !context.isEmpty()) {
                FeishuCardMessage.CardElement contextElement = new FeishuCardMessage.CardElement();
                contextElement.setTag("markdown");
                contextElement.setContent("**上下文信息：**\n" + context);
                contextElement.setTextAlign("left");
                contextElement.setMargin("8px 0px 0px 0px");
                elements.add(contextElement);
            }
            
            // 添加时间元素
            FeishuCardMessage.CardElement timeElement = new FeishuCardMessage.CardElement();
            timeElement.setTag("hr");
            timeElement.setMargin("8px 0px 0px 0px");
            elements.add(timeElement);
            
            FeishuCardMessage.CardElement timeTextElement = new FeishuCardMessage.CardElement();
            timeTextElement.setTag("markdown");
            timeTextElement.setContent("发生时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            timeTextElement.setTextAlign("left");
            timeTextElement.setMargin("4px 0px 0px 0px");
            elements.add(timeTextElement);
            
            body.setElements(elements);
            cardMessage.setBody(body);
            
            return sendCardMessage(cardMessage);
        } catch (Exception e) {
            log.error("发送错误通知失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendErrorNotificationWithGrafanaButton(String error, String context, String requestId) {
        try {
            // 创建错误通知卡片
            FeishuCardMessage cardMessage = new FeishuCardMessage();
            
            // 设置卡片头部
            FeishuCardMessage.CardHeader header = new FeishuCardMessage.CardHeader();
            FeishuCardMessage.CardTitle cardTitle = new FeishuCardMessage.CardTitle();
            cardTitle.setContent("❌ 系统错误通知");
            cardTitle.setTag("plain_text");
            header.setTitle(cardTitle);
            cardMessage.setHeader(header);
            
            // 设置卡片主体
            FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
            List<FeishuCardMessage.CardElement> elements = new ArrayList<>();
            
            // 添加错误信息元素
            FeishuCardMessage.CardElement errorElement = new FeishuCardMessage.CardElement();
            errorElement.setTag("markdown");
            errorElement.setContent("**错误信息：**\n" + error);
            errorElement.setTextAlign("left");
            errorElement.setMargin("0px 0px 0px 0px");
            elements.add(errorElement);
            
            // 添加上下文信息元素
            if (context != null && !context.isEmpty()) {
                FeishuCardMessage.CardElement contextElement = new FeishuCardMessage.CardElement();
                contextElement.setTag("markdown");
                contextElement.setContent("**上下文信息：**\n" + context);
                contextElement.setTextAlign("left");
                contextElement.setMargin("8px 0px 0px 0px");
                elements.add(contextElement);
            }
            
            // 添加Grafana链接（如果有requestId）
            if (requestId != null && !requestId.isEmpty() && !"-".equals(requestId)) {
                
                // 添加Grafana按钮
                FeishuCardMessage.CardElement buttonElement = new FeishuCardMessage.CardElement();
                buttonElement.setTag("button");
                
                // 按钮文本
                FeishuCardMessage.CardText buttonText = new FeishuCardMessage.CardText();
                buttonText.setTag("plain_text");
                buttonText.setContent("查看详细日志");
                buttonElement.setText(buttonText);
                
                // 按钮属性
                buttonElement.setType("primary_filled");
                buttonElement.setWidth("fill");
                buttonElement.setSize("large");
                
                // 按钮图标
                FeishuCardMessage.CardIcon icon = new FeishuCardMessage.CardIcon();
                icon.setTag("standard_icon");
                icon.setToken("search");
                buttonElement.setIcon(icon);
                
                // 按钮行为
                List<FeishuCardMessage.CardBehavior> behaviors = new ArrayList<>();
                FeishuCardMessage.CardBehavior behavior = new FeishuCardMessage.CardBehavior();
                behavior.setType("open_url");
                behavior.setDefaultUrl(buildGrafanaUrl(requestId));
                behavior.setPcUrl("");
                behavior.setIosUrl("");
                behavior.setAndroidUrl("");
                behaviors.add(behavior);
                buttonElement.setBehaviors(behaviors);
                
                buttonElement.setMargin("8px 8px 8px 8px");
                elements.add(buttonElement);
            }
            
            body.setElements(elements);
            cardMessage.setBody(body);
            
            // 添加调试日志
            log.info("生成的卡片消息JSON: {}", JSON.toJSONString(cardMessage));
            
            return sendCardMessage(cardMessage);
        } catch (Exception e) {
            log.error("发送带Grafana按钮的错误通知失败", e);
            return false;
        }
    }
    
    /**
     * 构建Grafana搜索链接
     *
     * @param requestId 请求ID
     * @return Grafana搜索链接
     */
    private String buildGrafanaUrl(String requestId) {
        String baseUrl = feishuBotProperties.getGrafanaBaseUrl();
        String encodedRequestId = java.net.URLEncoder.encode(requestId, StandardCharsets.UTF_8);
        
        // 构建Grafana Explore页面URL，使用配置化的参数
        return String.format("%s/explore?orgId=1&left={\"datasource\":\"%s\",\"queries\":[{\"refId\":\"A\",\"expr\":\"{app=\\\"%s\\\"}|=`%s`\",\"queryType\":\"range\",\"datasource\":{\"type\":\"loki\",\"uid\":\"%s\"},\"editorMode\":\"builder\"}],\"range\":{\"from\":\"%s\",\"to\":\"%s\"}}",
                baseUrl, 
                feishuBotProperties.getGrafanaDatasourceUid(), 
                feishuBotProperties.getGrafanaAppName(), 
                encodedRequestId, 
                feishuBotProperties.getGrafanaDatasourceUid(), 
                feishuBotProperties.getGrafanaTimeRange(), 
                feishuBotProperties.getGrafanaTimeRangeTo());
    }
    

    
    /**
     * 发送消息到飞书机器人
     */
    private boolean sendMessage(FeishuMessage message) {
        if (!feishuBotProperties.isEnabled()) {
            log.warn("飞书机器人未启用");
            return false;
        }
        
        if (feishuBotProperties.getWebhookUrl() == null || feishuBotProperties.getWebhookUrl().isEmpty()) {
            log.error("飞书机器人Webhook地址未配置");
            return false;
        }
        
        try {
            // 添加签名
            if (feishuBotProperties.getSecret() != null && !feishuBotProperties.getSecret().isEmpty()) {
                addSignature(message);
            }
            
            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            log.debug("飞书发送请求：{}", JSON.toJSONString(message) );
            
            HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(message), headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                feishuBotProperties.getWebhookUrl(),
                HttpMethod.POST,
                request,
                String.class
            );
            
            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("飞书消息发送成功");
                return true;
            } else {
                log.error("飞书消息发送失败，状态码：{}，响应：{}", response.getStatusCode(), response.getBody());
                return false;
            }
        } catch (Exception e) {
            log.error("发送飞书消息异常", e);
            return false;
        }
    }
    
    /**
     * 添加消息签名
     */
    private void addSignature(FeishuMessage message) {
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            String stringToSign = timestamp + "\n" + feishuBotProperties.getSecret();
            
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(feishuBotProperties.getSecret().getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = Base64.getEncoder().encodeToString(signData);
            
            message.setTimestamp(timestamp);
            message.setSign(sign);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("生成消息签名失败", e);
        }
    }

    /**
     * 构建简单的富文本消息
     *
     * @param title 标题
     * @param content 内容
     * @return 富文本消息对象
     */
    public FeishuPostMessage buildSimplePostMessage(String title, String content) {
        FeishuPostMessage postMessage = new FeishuPostMessage();
        FeishuPostMessage.PostContent postContent = new FeishuPostMessage.PostContent();
        FeishuPostMessage.PostContentDetail detail = new FeishuPostMessage.PostContentDetail();
        
        // 设置标题
        detail.setTitle(title);
        
        // 构建二维数组格式的内容
        List<List<FeishuPostMessage.ContentItem>> contentList = new ArrayList<>();
        
        // 第一行：标题和内容在同一行
        List<FeishuPostMessage.ContentItem> firstRow = new ArrayList<>();
        FeishuPostMessage.ContentItem titleItem = new FeishuPostMessage.ContentItem();
        titleItem.setTag("text");
        titleItem.setText(title + ": ");
        firstRow.add(titleItem);
        
        FeishuPostMessage.ContentItem contentItem = new FeishuPostMessage.ContentItem();
        contentItem.setTag("text");
        contentItem.setText(content);
        firstRow.add(contentItem);
        contentList.add(firstRow);
        
        detail.setContent(contentList);
        postContent.setZh_cn(detail);
        postMessage.setPost(postContent);
        
        return postMessage;
    }
    
    /**
     * 构建带链接的富文本消息
     *
     * @param title 标题
     * @param content 内容
     * @param linkText 链接文本
     * @param linkUrl 链接地址
     * @return 富文本消息对象
     */
    public FeishuPostMessage buildPostMessageWithLink(String title, String content, String linkText, String linkUrl) {
        FeishuPostMessage postMessage = new FeishuPostMessage();
        FeishuPostMessage.PostContent postContent = new FeishuPostMessage.PostContent();
        FeishuPostMessage.PostContentDetail detail = new FeishuPostMessage.PostContentDetail();
        
        // 设置标题
        detail.setTitle(title);
        
        // 构建二维数组格式的内容
        List<List<FeishuPostMessage.ContentItem>> contentList = new ArrayList<>();
        
        // 第一行：文本和链接在同一行
        List<FeishuPostMessage.ContentItem> firstRow = new ArrayList<>();
        FeishuPostMessage.ContentItem textItem = new FeishuPostMessage.ContentItem();
        textItem.setTag("text");
        textItem.setText(content + " ");
        firstRow.add(textItem);
        
        FeishuPostMessage.ContentItem linkItem = new FeishuPostMessage.ContentItem();
        linkItem.setTag("a");
        linkItem.setText(linkText);
        linkItem.setHref(linkUrl);
        firstRow.add(linkItem);
        contentList.add(firstRow);
        
        detail.setContent(contentList);
        postContent.setZh_cn(detail);
        postMessage.setPost(postContent);
        
        return postMessage;
    }
} 